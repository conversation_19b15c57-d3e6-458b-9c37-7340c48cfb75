<script setup lang="ts">
import { Head, useForm } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import Card from '@/components/ui/card/Card.vue'
import CardHeader from '@/components/ui/card/CardHeader.vue'
import CardTitle from '@/components/ui/card/CardTitle.vue'
import CardDescription from '@/components/ui/card/CardDescription.vue'
import CardContent from '@/components/ui/card/CardContent.vue'
import Input from '@/components/ui/input/Input.vue'
import Label from '@/components/ui/label/Label.vue'
import InputError from '@/components/InputError.vue'
import Icon from '@/components/Icon.vue'
import TextLink from '@/components/TextLink.vue'
import { type BreadcrumbItem } from '@/types'

interface Wilayah {
  id_wilayah: number
  nama_wilayah: string
}

const breadcrumbItems: BreadcrumbItem[] = [
  {
    title: 'Manajemen Peserta',
    href: '/admin/peserta',
  },
  {
    title: 'Tambah Peserta Baru',
    href: '/admin/peserta/create',
  },
]

defineProps<{
  wilayah: Wilayah[]
}>()

const form = useForm({
  username: '',
  email: '',
  password: '',
  nik: '',
  nama_lengkap: '',
  tempat_lahir: '',
  tanggal_lahir: '',
  jenis_kelamin: '',
  alamat: '',
  id_wilayah: '',
  no_telepon: '',
  nama_ayah: '',
  nama_ibu: '',
  pekerjaan: '',
  instansi_asal: ''
})

function submit() {
  form.post(route('admin.peserta.store'), {
    onSuccess: () => {
      // Success message will be handled by the backend
    }
  })
}
</script>

<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Tambah Peserta Baru" />

    <!-- Header Section -->
    <div class="space-y-4">
      <div class="flex items-center space-x-3">
        <Button as-child class="text-muted-foreground bg-gray-100 cursor-pointer hover:text-foreground">
          <TextLink :href="route('admin.peserta.index')">
            <Icon name="arrowLeft" class="w-4 h-4 mr-2" />
            Kembali
          </TextLink>
        </Button>
        <div>
          <Heading title="Tambah Peserta Baru" description="Lengkapi informasi peserta untuk mendaftarkan anggota baru" />
        </div>
      </div>

      <!-- Form Container -->
      <div class="max-w-5xl mx-auto">
        <form @submit.prevent="submit" class="space-y-4">

          <!-- Account & Personal Information -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">

            <!-- Account Information Card -->
            <Card>
              <CardHeader class="pb-3">
                <div class="flex items-center space-x-2">
                  <div class="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center">
                    <Icon name="user" class="w-3 h-3 text-primary" />
                  </div>
                  <div>
                    <CardTitle class="text-base">Informasi Akun</CardTitle>
                    <CardDescription class="text-xs">
                      Data login dan autentikasi
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent class="space-y-3">
                <div class="space-y-1">
                  <Label for="username" class="text-xs font-medium">
                    Username <span class="text-red-500">*</span>
                  </Label>
                  <Input
                    id="username"
                    v-model="form.username"
                    type="text"
                    :error="!!form.errors.username"
                    placeholder="Username unik"
                    class="h-8 text-sm"
                    required
                  />
                  <InputError :message="form.errors.username" />
                </div>

                <div class="space-y-1">
                  <Label for="email" class="text-xs font-medium">
                    Email <span class="text-red-500">*</span>
                  </Label>
                  <Input
                    id="email"
                    v-model="form.email"
                    type="email"
                    :error="!!form.errors.email"
                    placeholder="<EMAIL>"
                    class="h-8 text-sm"
                    required
                  />
                  <InputError :message="form.errors.email" />
                </div>

                <div class="space-y-1">
                  <Label for="password" class="text-xs font-medium">
                    Password <span class="text-red-500">*</span>
                  </Label>
                  <Input
                    id="password"
                    v-model="form.password"
                    type="password"
                    :error="!!form.errors.password"
                    placeholder="Min. 8 karakter"
                    class="h-8 text-sm"
                    required
                  />
                  <InputError :message="form.errors.password" />
                </div>
              </CardContent>
            </Card>

            <!-- Personal Information Card -->
            <Card>
              <CardHeader class="pb-3">
                <div class="flex items-center space-x-2">
                  <div class="w-6 h-6 bg-blue-500/10 rounded-full flex items-center justify-center">
                    <Icon name="id-card" class="w-3 h-3 text-blue-500" />
                  </div>
                  <div>
                    <CardTitle class="text-base">Informasi Pribadi</CardTitle>
                    <CardDescription class="text-xs">
                      Data identitas dan biodata
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent class="space-y-3">
                <div class="space-y-1">
                  <Label for="nik" class="text-xs font-medium">
                    NIK <span class="text-red-500">*</span>
                  </Label>
                  <Input
                    id="nik"
                    v-model="form.nik"
                    type="text"
                    :error="!!form.errors.nik"
                    placeholder="16 digit NIK"
                    maxlength="16"
                    class="h-8 text-sm font-mono"
                    required
                  />
                  <InputError :message="form.errors.nik" />
                </div>

                <div class="space-y-1">
                  <Label for="nama_lengkap" class="text-xs font-medium">
                    Nama Lengkap <span class="text-red-500">*</span>
                  </Label>
                  <Input
                    id="nama_lengkap"
                    v-model="form.nama_lengkap"
                    type="text"
                    :error="!!form.errors.nama_lengkap"
                    placeholder="Nama lengkap sesuai KTP"
                    class="h-8 text-sm"
                    required
                  />
                  <InputError :message="form.errors.nama_lengkap" />
                </div>

                <div class="grid grid-cols-2 gap-3">
                  <div class="space-y-1">
                    <Label for="tempat_lahir" class="text-xs font-medium">
                      Tempat Lahir <span class="text-red-500">*</span>
                    </Label>
                    <Input
                      id="tempat_lahir"
                      v-model="form.tempat_lahir"
                      type="text"
                      :error="!!form.errors.tempat_lahir"
                      placeholder="Kota"
                      class="h-8 text-sm"
                      required
                    />
                    <InputError :message="form.errors.tempat_lahir" />
                  </div>

                  <div class="space-y-1">
                    <Label for="tanggal_lahir" class="text-xs font-medium">
                      Tanggal Lahir <span class="text-red-500">*</span>
                    </Label>
                    <Input
                      id="tanggal_lahir"
                      v-model="form.tanggal_lahir"
                      type="date"
                      :error="!!form.errors.tanggal_lahir"
                      class="h-8 text-sm"
                      required
                    />
                    <InputError :message="form.errors.tanggal_lahir" />
                  </div>
                </div>

                <div class="space-y-1">
                  <Label for="jenis_kelamin" class="text-xs font-medium">
                    Jenis Kelamin <span class="text-red-500">*</span>
                  </Label>
                  <select
                    id="jenis_kelamin"
                    v-model="form.jenis_kelamin"
                    class="flex h-8 w-full rounded-md border border-input bg-background px-3 py-1 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    :class="{ 'border-red-500': form.errors.jenis_kelamin }"
                    required
                  >
                    <option value="">Pilih</option>
                    <option value="L">Laki-laki</option>
                    <option value="P">Perempuan</option>
                  </select>
                  <InputError :message="form.errors.jenis_kelamin" />
                </div>
              </CardContent>
            </Card>
          </div>

          <!-- Contact Information -->
          <Card>
            <CardHeader class="pb-3">
              <div class="flex items-center space-x-2">
                <div class="w-6 h-6 bg-green-500/10 rounded-full flex items-center justify-center">
                  <Icon name="map-pin" class="w-3 h-3 text-green-500" />
                </div>
                <div>
                  <CardTitle class="text-base">Informasi Kontak</CardTitle>
                  <CardDescription class="text-xs">
                    Alamat dan informasi kontak
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent class="space-y-3">
              <div class="space-y-1">
                <Label for="alamat" class="text-xs font-medium">
                  Alamat <span class="text-red-500">*</span>
                </Label>
                <textarea
                  id="alamat"
                  v-model="form.alamat"
                  rows="2"
                  class="flex min-h-[60px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  :class="{ 'border-red-500': form.errors.alamat }"
                  placeholder="Alamat lengkap sesuai KTP"
                  required
                ></textarea>
                <InputError :message="form.errors.alamat" />
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div class="space-y-1">
                  <Label for="id_wilayah" class="text-xs font-medium">
                    Wilayah <span class="text-red-500">*</span>
                  </Label>
                  <select
                    id="id_wilayah"
                    v-model="form.id_wilayah"
                    class="flex h-8 w-full rounded-md border border-input bg-background px-3 py-1 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    :class="{ 'border-red-500': form.errors.id_wilayah }"
                    required
                  >
                    <option value="">Pilih wilayah</option>
                    <option v-for="w in wilayah" :key="w.id_wilayah" :value="w.id_wilayah">
                      {{ w.nama_wilayah }}
                    </option>
                  </select>
                  <InputError :message="form.errors.id_wilayah" />
                </div>

                <div class="space-y-1">
                  <Label for="no_telepon" class="text-xs font-medium">
                    No. Telepon
                  </Label>
                  <Input
                    id="no_telepon"
                    v-model="form.no_telepon"
                    type="tel"
                    :error="!!form.errors.no_telepon"
                    placeholder="08xxxxxxxxxx"
                    class="h-8 text-sm"
                  />
                  <InputError :message="form.errors.no_telepon" />
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- Additional Information -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">

            <!-- Family Information -->
            <Card>
              <CardHeader class="pb-3">
                <div class="flex items-center space-x-2">
                  <div class="w-6 h-6 bg-purple-500/10 rounded-full flex items-center justify-center">
                    <Icon name="users" class="w-3 h-3 text-purple-500" />
                  </div>
                  <div>
                    <CardTitle class="text-base">Informasi Keluarga</CardTitle>
                    <CardDescription class="text-xs">
                      Data keluarga (opsional)
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent class="space-y-3">
                <div class="space-y-1">
                  <Label for="nama_ayah" class="text-xs font-medium">
                    Nama Ayah
                  </Label>
                  <Input
                    id="nama_ayah"
                    v-model="form.nama_ayah"
                    type="text"
                    :error="!!form.errors.nama_ayah"
                    placeholder="Nama ayah kandung"
                    class="h-8 text-sm"
                  />
                  <InputError :message="form.errors.nama_ayah" />
                </div>

                <div class="space-y-1">
                  <Label for="nama_ibu" class="text-xs font-medium">
                    Nama Ibu
                  </Label>
                  <Input
                    id="nama_ibu"
                    v-model="form.nama_ibu"
                    type="text"
                    :error="!!form.errors.nama_ibu"
                    placeholder="Nama ibu kandung"
                    class="h-8 text-sm"
                  />
                  <InputError :message="form.errors.nama_ibu" />
                </div>
              </CardContent>
            </Card>

            <!-- Professional Information -->
            <Card>
              <CardHeader class="pb-3">
                <div class="flex items-center space-x-2">
                  <div class="w-6 h-6 bg-orange-500/10 rounded-full flex items-center justify-center">
                    <Icon name="briefcase" class="w-3 h-3 text-orange-500" />
                  </div>
                  <div>
                    <CardTitle class="text-base">Informasi Profesi</CardTitle>
                    <CardDescription class="text-xs">
                      Data pekerjaan dan instansi
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent class="space-y-3">
                <div class="space-y-1">
                  <Label for="pekerjaan" class="text-xs font-medium">
                    Pekerjaan
                  </Label>
                  <Input
                    id="pekerjaan"
                    v-model="form.pekerjaan"
                    type="text"
                    :error="!!form.errors.pekerjaan"
                    placeholder="Jabatan atau profesi"
                    class="h-8 text-sm"
                  />
                  <InputError :message="form.errors.pekerjaan" />
                </div>

                <div class="space-y-1">
                  <Label for="instansi_asal" class="text-xs font-medium">
                    Instansi Asal
                  </Label>
                  <Input
                    id="instansi_asal"
                    v-model="form.instansi_asal"
                    type="text"
                    :error="!!form.errors.instansi_asal"
                    placeholder="Nama perusahaan/instansi"
                    class="h-8 text-sm"
                  />
                  <InputError :message="form.errors.instansi_asal" />
                </div>
              </CardContent>
            </Card>
          </div>

          <!-- Action Buttons -->
          <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
            <div class="text-sm text-muted-foreground">
              <span class="text-red-500">*</span> menandakan field wajib diisi
            </div>
            <div class="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
              <Button
                as-child
                variant="outline"
                class="w-full sm:w-auto"
              >
                <TextLink :href="route('admin.peserta.index')">
                  <Icon name="x" class="w-4 h-4 mr-2" />
                  Batal
                </TextLink>
              </Button>
              <Button
                type="submit"
                variant="default"
                :disabled="form.processing"
                class="w-full sm:w-auto"
              >
                <Icon
                  v-if="form.processing"
                  name="loader-2"
                  class="w-4 h-4 mr-2 animate-spin"
                />
                <Icon
                  v-else
                  name="save"
                  class="w-4 h-4 mr-2"
                />
                {{ form.processing ? 'Menyimpan...' : 'Simpan' }}
              </Button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </AppLayout>
</template>

