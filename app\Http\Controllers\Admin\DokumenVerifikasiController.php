<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\DokumenPeserta;
use App\Models\Pendaftaran;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class DokumenVerifikasiController extends Controller
{
    /**
     * Display a listing of documents for verification
     */
    public function index(Request $request): Response
    {
        $query = DokumenPeserta::with([
            'pendaftaran.peserta.wilayah',
            'pendaftaran.golongan.cabangLomba',
            'uploadedBy',
            'verifiedBy'
        ]);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status_verifikasi', $request->status);
        }

        // Filter by document type
        if ($request->filled('jenis_dokumen')) {
            $query->where('jenis_dokumen', $request->jenis_dokumen);
        }

        // Filter by region (for admin daerah)
        if (Auth::user()->role === 'admin_daerah') {
            $query->whereHas('pendaftaran.peserta', function ($q) {
                $q->where('id_wilayah', Auth::user()->id_wilayah);
            });
        }

        // Search by participant name or registration number
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('pendaftaran', function ($q) use ($search) {
                $q->where('nomor_pendaftaran', 'like', "%{$search}%")
                  ->orWhereHas('peserta', function ($pq) use ($search) {
                      $pq->where('nama_lengkap', 'like', "%{$search}%");
                  });
            });
        }

        $dokumen = $query->orderBy('created_at', 'desc')->paginate(20);

        $jenisOptions = [
            'foto' => 'Foto Peserta',
            'ktp' => 'KTP',
            'kartu_keluarga' => 'Kartu Keluarga',
            'surat_rekomendasi' => 'Surat Rekomendasi',
            'ijazah' => 'Ijazah Terakhir',
            'sertifikat' => 'Sertifikat',
            'lainnya' => 'Lainnya'
        ];

        return Inertia::render('Admin/DokumenVerifikasi/Index', [
            'dokumen' => $dokumen,
            'filters' => $request->only(['status', 'jenis_dokumen', 'search']),
            'jenisOptions' => $jenisOptions
        ]);
    }

    /**
     * Show the form for verifying a specific document
     */
    public function show(DokumenPeserta $dokumen): Response
    {
        $dokumen->load([
            'pendaftaran.peserta.wilayah',
            'pendaftaran.golongan.cabangLomba',
            'uploadedBy',
            'verifiedBy'
        ]);

        // Check access for admin daerah
        if (Auth::user()->role === 'admin_daerah') {
            if ($dokumen->pendaftaran->peserta->id_wilayah !== Auth::user()->id_wilayah) {
                abort(403, 'Akses ditolak');
            }
        }

        return Inertia::render('Admin/DokumenVerifikasi/Show', [
            'dokumen' => $dokumen
        ]);
    }

    /**
     * Verify a document (approve or reject)
     */
    public function verify(Request $request, DokumenPeserta $dokumen)
    {
        // Check access for admin daerah
        if (Auth::user()->role === 'admin_daerah') {
            if ($dokumen->pendaftaran->peserta->id_wilayah !== Auth::user()->id_wilayah) {
                abort(403, 'Akses ditolak');
            }
        }

        $validated = $request->validate([
            'status_verifikasi' => 'required|in:approved,rejected',
            'catatan_verifikasi' => 'nullable|string|max:500'
        ]);

        $dokumen->update([
            'status_verifikasi' => $validated['status_verifikasi'],
            'catatan_verifikasi' => $validated['catatan_verifikasi'],
            'verified_by' => Auth::id(),
            'verified_at' => now()
        ]);

        $message = $validated['status_verifikasi'] === 'approved' 
            ? 'Dokumen berhasil disetujui.' 
            : 'Dokumen berhasil ditolak.';

        return back()->with('success', $message);
    }

    /**
     * Download a document for verification
     */
    public function download(DokumenPeserta $dokumen)
    {
        // Check access for admin daerah
        if (Auth::user()->role === 'admin_daerah') {
            if ($dokumen->pendaftaran->peserta->id_wilayah !== Auth::user()->id_wilayah) {
                abort(403, 'Akses ditolak');
            }
        }

        if (!file_exists(storage_path('app/public/' . $dokumen->path_file))) {
            abort(404, 'File tidak ditemukan.');
        }

        return response()->download(
            storage_path('app/public/' . $dokumen->path_file),
            $dokumen->nama_file
        );
    }

    /**
     * Bulk verify documents
     */
    public function bulkVerify(Request $request)
    {
        $validated = $request->validate([
            'dokumen_ids' => 'required|array',
            'dokumen_ids.*' => 'exists:dokumen_peserta,id_dokumen',
            'status_verifikasi' => 'required|in:approved,rejected',
            'catatan_verifikasi' => 'nullable|string|max:500'
        ]);

        $query = DokumenPeserta::whereIn('id_dokumen', $validated['dokumen_ids']);

        // Check access for admin daerah
        if (Auth::user()->role === 'admin_daerah') {
            $query->whereHas('pendaftaran.peserta', function ($q) {
                $q->where('id_wilayah', Auth::user()->id_wilayah);
            });
        }

        $updated = $query->update([
            'status_verifikasi' => $validated['status_verifikasi'],
            'catatan_verifikasi' => $validated['catatan_verifikasi'],
            'verified_by' => Auth::id(),
            'verified_at' => now()
        ]);

        $message = $validated['status_verifikasi'] === 'approved' 
            ? "Berhasil menyetujui {$updated} dokumen." 
            : "Berhasil menolak {$updated} dokumen.";

        return back()->with('success', $message);
    }
}
