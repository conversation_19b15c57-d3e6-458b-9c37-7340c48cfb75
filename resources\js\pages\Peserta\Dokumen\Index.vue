<script setup lang="ts">
import { ref } from 'vue'
import { useForm } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import { Head } from '@inertiajs/vue3'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import InputError from '@/components/InputError.vue'
import Icon from '@/components/Icon.vue'

interface CabangLomba {
  nama_cabang: string
}

interface Golongan {
  nama_golongan: string
  cabang_lomba: CabangLomba
}

interface DokumenPeserta {
  id_dokumen: number
  jenis_dokumen: string
  nama_file: string
  ukuran_file: number
  status_verifikasi: string
  catatan_verifikasi: string | null
  verified_at: string | null
  keterangan: string | null
}

interface Pendaftaran {
  id_pendaftaran: number
  nomor_pendaftaran: string
  status_pendaftaran: string
  golongan: Golongan
  dokumen_peserta: DokumenPeserta[]
}

const props = defineProps<{
  pendaftaran: Pendaftaran
  requiredDocuments: Record<string, string>
}>()

const uploadForm = useForm({
  jenis_dokumen: '',
  file: null as File | null,
  keterangan: ''
})

const replaceForm = useForm({
  file: null as File | null,
  keterangan: ''
})

const selectedDocument = ref<DokumenPeserta | null>(null)
const showUploadDialog = ref(false)
const showReplaceDialog = ref(false)

function uploadDocument() {
  uploadForm.post(route('peserta.dokumen.store', props.pendaftaran.id_pendaftaran), {
    onSuccess: () => {
      uploadForm.reset()
      showUploadDialog.value = false
    }
  })
}

function replaceDocument() {
  if (!selectedDocument.value) return
  
  replaceForm.post(route('peserta.dokumen.replace', [props.pendaftaran.id_pendaftaran, selectedDocument.value.id_dokumen]), {
    onSuccess: () => {
      replaceForm.reset()
      showReplaceDialog.value = false
      selectedDocument.value = null
    }
  })
}

function deleteDocument(dokumen: DokumenPeserta) {
  if (confirm('Apakah Anda yakin ingin menghapus dokumen ini?')) {
    useForm({}).delete(route('peserta.dokumen.destroy', [props.pendaftaran.id_pendaftaran, dokumen.id_dokumen]))
  }
}

function openReplaceDialog(dokumen: DokumenPeserta) {
  selectedDocument.value = dokumen
  replaceForm.keterangan = dokumen.keterangan || ''
  showReplaceDialog.value = true
}

function getStatusColor(status: string): string {
  const colors = {
    pending: 'bg-yellow-100 text-yellow-800',
    approved: 'bg-green-100 text-green-800',
    rejected: 'bg-red-100 text-red-800'
  }
  return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800'
}

function getStatusText(status: string): string {
  const texts = {
    pending: 'Menunggu Verifikasi',
    approved: 'Disetujui',
    rejected: 'Ditolak'
  }
  return texts[status as keyof typeof texts] || status
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

function getAvailableDocumentTypes(): Array<{value: string, label: string}> {
  const uploaded = props.pendaftaran.dokumen_peserta.map(d => d.jenis_dokumen)
  return Object.entries(props.requiredDocuments)
    .filter(([key]) => !uploaded.includes(key))
    .map(([key, label]) => ({ value: key, label }))
}

function canEdit(): boolean {
  return ['draft', 'payment_pending', 'paid'].includes(props.pendaftaran.status_pendaftaran)
}
</script>

<template>
  <AppLayout>
    <template #header>
      <div class="flex items-center space-x-4">
        <Button
          as="link"
          :href="route('peserta.pendaftaran.show', pendaftaran.id_pendaftaran)"
          variant="ghost"
          size="sm"
        >
          <Icon name="arrow-left" class="w-4 h-4 mr-2" />
          Kembali
        </Button>
        <Heading>Kelola Dokumen</Heading>
      </div>
    </template>

    <Head title="Kelola Dokumen" />

    <div class="space-y-6">
      <!-- Registration Info -->
      <Card>
        <CardHeader>
          <CardTitle>{{ pendaftaran.golongan.nama_golongan }}</CardTitle>
          <CardDescription>{{ pendaftaran.golongan.cabang_lomba.nama_cabang }} - {{ pendaftaran.nomor_pendaftaran }}</CardDescription>
        </CardHeader>
      </Card>

      <!-- Upload Alert -->
      <Alert v-if="!canEdit()" class="border-yellow-200 bg-yellow-50">
        <Icon name="info" class="h-4 w-4" />
        <AlertDescription>
          Dokumen tidak dapat diubah karena pendaftaran sudah dalam tahap verifikasi atau telah disetujui.
        </AlertDescription>
      </Alert>

      <!-- Document List -->
      <Card>
        <CardHeader>
          <div class="flex items-center justify-between">
            <CardTitle>Dokumen yang Diupload</CardTitle>
            <Dialog v-model:open="showUploadDialog">
              <DialogTrigger as-child>
                <Button v-if="canEdit() && getAvailableDocumentTypes().length > 0">
                  <Icon name="plus" class="w-4 h-4 mr-2" />
                  Upload Dokumen
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Upload Dokumen Baru</DialogTitle>
                  <DialogDescription>
                    Pilih jenis dokumen dan upload file yang diperlukan.
                  </DialogDescription>
                </DialogHeader>
                <form @submit.prevent="uploadDocument" class="space-y-4">
                  <div class="grid gap-2">
                    <Label for="jenis_dokumen">Jenis Dokumen</Label>
                    <select 
                      id="jenis_dokumen"
                      v-model="uploadForm.jenis_dokumen"
                      required
                      class="flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
                    >
                      <option value="">Pilih jenis dokumen</option>
                      <option v-for="doc in getAvailableDocumentTypes()" :key="doc.value" :value="doc.value">
                        {{ doc.label }}
                      </option>
                    </select>
                    <InputError :message="uploadForm.errors.jenis_dokumen" />
                  </div>

                  <div class="grid gap-2">
                    <Label for="file">File</Label>
                    <Input 
                      id="file"
                      type="file"
                      accept=".jpg,.jpeg,.png,.pdf"
                      @change="uploadForm.file = $event.target.files[0]"
                      required
                    />
                    <p class="text-xs text-gray-500">Format: JPG, PNG, PDF. Maksimal 5MB.</p>
                    <InputError :message="uploadForm.errors.file" />
                  </div>

                  <div class="grid gap-2">
                    <Label for="keterangan">Keterangan (Opsional)</Label>
                    <Textarea 
                      id="keterangan"
                      v-model="uploadForm.keterangan"
                      placeholder="Tambahkan keterangan jika diperlukan..."
                      rows="3"
                    />
                    <InputError :message="uploadForm.errors.keterangan" />
                  </div>

                  <div class="flex justify-end space-x-2">
                    <Button type="button" variant="outline" @click="showUploadDialog = false">
                      Batal
                    </Button>
                    <Button type="submit" :disabled="uploadForm.processing">
                      <Icon v-if="uploadForm.processing" name="loader-2" class="w-4 h-4 mr-2 animate-spin" />
                      Upload
                    </Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          </div>
          <CardDescription>Daftar dokumen yang telah Anda upload</CardDescription>
        </CardHeader>
        <CardContent>
          <div v-if="pendaftaran.dokumen_peserta.length === 0" class="text-center py-8">
            <Icon name="file-x" class="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 class="text-lg font-medium text-gray-900 mb-2">Belum Ada Dokumen</h3>
            <p class="text-gray-600 mb-4">Silakan upload dokumen yang diperlukan untuk melengkapi pendaftaran.</p>
          </div>
          
          <div v-else class="space-y-4">
            <div 
              v-for="dokumen in pendaftaran.dokumen_peserta" 
              :key="dokumen.id_dokumen"
              class="flex items-center justify-between p-4 border rounded-lg"
            >
              <div class="flex items-center space-x-4">
                <Icon name="file-text" class="h-8 w-8 text-gray-500" />
                <div>
                  <h4 class="font-medium">{{ requiredDocuments[dokumen.jenis_dokumen] || dokumen.jenis_dokumen }}</h4>
                  <p class="text-sm text-gray-600">{{ dokumen.nama_file }}</p>
                  <p class="text-xs text-gray-500">{{ formatFileSize(dokumen.ukuran_file) }}</p>
                  <p v-if="dokumen.keterangan" class="text-xs text-gray-500 mt-1">{{ dokumen.keterangan }}</p>
                  <p v-if="dokumen.catatan_verifikasi" class="text-xs text-red-600 mt-1">
                    Catatan: {{ dokumen.catatan_verifikasi }}
                  </p>
                </div>
              </div>
              
              <div class="flex items-center space-x-2">
                <Badge :class="getStatusColor(dokumen.status_verifikasi)">
                  {{ getStatusText(dokumen.status_verifikasi) }}
                </Badge>
                
                <div class="flex space-x-1">
                  <Button
                    as="link"
                    :href="route('peserta.dokumen.download', [pendaftaran.id_pendaftaran, dokumen.id_dokumen])"
                    size="sm"
                    variant="outline"
                  >
                    <Icon name="download" class="w-4 h-4" />
                  </Button>
                  
                  <Button
                    v-if="canEdit() && dokumen.status_verifikasi !== 'approved'"
                    @click="openReplaceDialog(dokumen)"
                    size="sm"
                    variant="outline"
                  >
                    <Icon name="edit" class="w-4 h-4" />
                  </Button>
                  
                  <Button
                    v-if="canEdit() && dokumen.status_verifikasi !== 'approved'"
                    @click="deleteDocument(dokumen)"
                    size="sm"
                    variant="outline"
                    class="text-red-600 hover:text-red-700"
                  >
                    <Icon name="trash-2" class="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Required Documents Info -->
      <Card>
        <CardHeader>
          <CardTitle>Dokumen yang Diperlukan</CardTitle>
          <CardDescription>Daftar dokumen yang harus dilengkapi</CardDescription>
        </CardHeader>
        <CardContent>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div v-for="(label, key) in requiredDocuments" :key="key" class="flex items-center space-x-3">
              <Icon 
                :name="pendaftaran.dokumen_peserta.some(d => d.jenis_dokumen === key) ? 'check-circle' : 'circle'"
                :class="pendaftaran.dokumen_peserta.some(d => d.jenis_dokumen === key) ? 'text-green-600' : 'text-gray-400'"
                class="h-5 w-5"
              />
              <span class="text-sm">{{ label }}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Replace Document Dialog -->
      <Dialog v-model:open="showReplaceDialog">
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Ganti Dokumen</DialogTitle>
            <DialogDescription>
              Upload file baru untuk mengganti dokumen yang sudah ada.
            </DialogDescription>
          </DialogHeader>
          <form @submit.prevent="replaceDocument" class="space-y-4">
            <div class="grid gap-2">
              <Label for="replace_file">File Baru</Label>
              <Input 
                id="replace_file"
                type="file"
                accept=".jpg,.jpeg,.png,.pdf"
                @change="replaceForm.file = $event.target.files[0]"
                required
              />
              <p class="text-xs text-gray-500">Format: JPG, PNG, PDF. Maksimal 5MB.</p>
              <InputError :message="replaceForm.errors.file" />
            </div>

            <div class="grid gap-2">
              <Label for="replace_keterangan">Keterangan (Opsional)</Label>
              <Textarea 
                id="replace_keterangan"
                v-model="replaceForm.keterangan"
                placeholder="Tambahkan keterangan jika diperlukan..."
                rows="3"
              />
              <InputError :message="replaceForm.errors.keterangan" />
            </div>

            <div class="flex justify-end space-x-2">
              <Button type="button" variant="outline" @click="showReplaceDialog = false">
                Batal
              </Button>
              <Button type="submit" :disabled="replaceForm.processing">
                <Icon v-if="replaceForm.processing" name="loader-2" class="w-4 h-4 mr-2 animate-spin" />
                Ganti Dokumen
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  </AppLayout>
</template>
