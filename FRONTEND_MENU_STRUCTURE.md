# MTQ System - Frontend Menu Structure

## Overview

The frontend menu system has been updated to reflect the comprehensive routing architecture of the MTQ system. The menu is role-based and dynamically displays different navigation items based on the user's role.

## Menu Structure by Role

### 1. SuperAdmin & Admin
**Route Prefix**: `/admin`

- **Dashboard** (`/admin/dashboard`)
- **Manajemen User** (`/admin/users`)
- **Manajemen Wilayah** (`/admin/wilayah`)
- **C<PERSON>ng <PERSON>mba** (`/admin/cabang-lomba`)
- **<PERSON><PERSON><PERSON>** (`/admin/golongan`)
- **Mimbar** (`/admin/mimbar`)
- **<PERSON><PERSON>** (`/admin/dewan-hakim`)
- **Pelaksanaan MTQ** (`/admin/pelaksanaan`)
- **<PERSON><PERSON><PERSON>en Peserta** (`/admin/peserta`)
- **Pendaftaran Lomba** (`/admin/pendaftaran`)
- **Pembayaran** (`/admin/pembayaran`)
- **<PERSON><PERSON><PERSON>** (`/admin/laporan`)

### 2. <PERSON><PERSON> (Regional Administrator)
**Route Prefix**: `/admin-daerah`

- **Dashboard** (`/admin-daerah/dashboard`)
- **Manajemen Peserta** (`/admin-daerah/peserta`)
- **Daftar Langsung** (`/admin-daerah/peserta/create`) - *Key Feature*
- **Pendaftaran Lomba** (`/admin-daerah/pendaftaran`)
- **Laporan Daerah** (`/admin-daerah/laporan`)

### 3. Peserta (Participant)
**Route Prefix**: `/peserta`

- **Dashboard** (`/peserta/dashboard`)
- **Profil Saya** (`/peserta/profile`)
- **Pendaftaran Lomba** (`/peserta/pendaftaran`)
- **Dokumen** (`/peserta/dokumen`)
- **Pembayaran** (`/peserta/pembayaran`)

### 4. Dewan Hakim (Judge)
**Route Prefix**: `/dewan-hakim`

- **Dashboard** (`/dewan-hakim/dashboard`)
- **Profil Hakim** (`/dewan-hakim/profile`)
- **Sistem Penilaian** (`/dewan-hakim/penilaian`)

## Technical Implementation

### Components Updated

1. **AppSidebar.vue**
   - Enhanced with comprehensive role-based navigation
   - Added new Lucide icons for better visual representation
   - Simplified structure without nested menus for initial implementation

2. **NavMain.vue**
   - Maintained simple structure for easy navigation
   - Supports dynamic menu rendering based on user role
   - Active state detection for current page

3. **Type Definitions** (`types/index.d.ts`)
   - Enhanced NavItem interface to support future nested menu items
   - Added optional `items` property for sub-navigation

### Icons Used

- **LayoutGrid**: Dashboard
- **UserCheck**: User Management
- **MapPin**: Regional Management
- **Trophy**: Competition Categories
- **Award**: Competition Groups
- **Building2**: Venues (Mimbar)
- **Gavel**: Judges
- **Calendar**: Event Management
- **Users**: Participants
- **FileText**: Registrations
- **CreditCard**: Payments
- **BarChart3**: Reports
- **UserPlus**: Direct Registration (Admin Daerah)
- **Upload**: Document Management
- **ClipboardList**: Scoring System

## Key Features

### 1. Role-Based Navigation
- Menu items are dynamically filtered based on user role
- Each role sees only relevant navigation options
- Proper access control through middleware

### 2. Admin Daerah Special Features
- **Daftar Langsung**: Direct participant registration capability
- Regional-specific management tools
- Streamlined workflow for regional administrators

### 3. Responsive Design
- Collapsible sidebar with icon-only mode
- Tooltip support for collapsed state
- Mobile-friendly navigation

### 4. Active State Management
- Current page highlighting
- Visual feedback for user navigation
- Consistent user experience

## Future Enhancements

### Planned Features
1. **Nested Menu Support**: Sub-navigation for complex sections
2. **Badge Notifications**: Pending items count display
3. **Quick Actions**: Contextual action buttons
4. **Search Integration**: Global search within navigation
5. **Favorites**: User-customizable quick access menu

### Technical Improvements
1. **Menu Permissions**: Granular permission-based menu filtering
2. **Dynamic Loading**: Lazy-loaded menu sections
3. **Keyboard Navigation**: Full keyboard accessibility
4. **Menu State Persistence**: Remember expanded/collapsed state

## Usage Guidelines

### For Developers
1. **Adding New Menu Items**: Update the role-specific sections in `AppSidebar.vue`
2. **Icon Selection**: Use Lucide icons for consistency
3. **Route Naming**: Follow the established naming conventions
4. **Permission Checking**: Ensure proper middleware protection

### For Administrators
1. **User Role Assignment**: Assign appropriate roles for correct menu access
2. **Regional Setup**: Configure regional administrators for proper access
3. **Permission Management**: Review user permissions regularly

## Menu Hierarchy

```
MTQ System
├── Public Routes
│   ├── Home
│   └── Competition Info
├── Admin Routes
│   ├── System Management
│   ├── User Management
│   ├── Competition Setup
│   ├── Participant Management
│   └── Reports
├── Admin Daerah Routes
│   ├── Regional Dashboard
│   ├── Direct Registration
│   └── Regional Reports
├── Peserta Routes
│   ├── Personal Dashboard
│   ├── Self Registration
│   ├── Document Management
│   └── Payment Management
└── Dewan Hakim Routes
    ├── Judge Dashboard
    ├── Profile Management
    └── Scoring System
```

This menu structure provides a comprehensive and intuitive navigation system that supports all user roles while maintaining clear separation of concerns and proper access control.
